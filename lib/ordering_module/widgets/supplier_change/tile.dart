import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/ordering_module/cubits/supplier_change/cubit.dart';
import 'package:app/shared/widgets/core_product_tag.dart';
import 'package:app/shared/widgets/wrapper/v3_theme_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SupplierChangeTile extends StatelessWidget {
  final ProductOfferModel offer;

  const SupplierChangeTile({
    super.key,
    required this.offer,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SupplierChangeCubit>();

    return BlocBuilder<SupplierChangeCubit, SupplierChangeState>(
      buildWhen: (p, c) =>
          p.extension.selectedPositionId != c.extension.selectedPositionId,
      builder: (context, state) {
        final offerPositionId = offer.positionId.toString();
        final isSelected = offerPositionId == state.extension.selectedPositionId;

        // Debug information for selected items
        if (isSelected) {
          print('SupplierChangeTile - Selected: ${offer.supplier} (ID: $offerPositionId)');
        }

        return ListTile(
          title: Row(
            children: [
              Flexible(
                child: Text(
                  offer.supplier,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (offer.isCoreItem)
                const Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: V3ThemeWrapper(child: CoreProductTag()),
                ),
            ],
          ),
          trailing: Radio<String>(
            value: offerPositionId,
            groupValue: state.extension.selectedPositionId,
            onChanged: (_) => cubit.selectSupplier(offerPositionId),
          ),
          onTap: () => cubit.selectSupplier(offerPositionId),
        );
      },
    );
  }
}
