import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/ordering_module/cubits/supplier_change/cubit.dart';
import 'package:app/shared/widgets/core_product_tag.dart';
import 'package:app/shared/widgets/wrapper/v3_theme_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SupplierChangeTile extends StatelessWidget {
  final ProductOfferModel offer;

  const SupplierChangeTile({
    super.key,
    required this.offer,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SupplierChangeCubit>();

    return BlocBuilder<SupplierChangeCubit, SupplierChangeState>(
      buildWhen: (p, c) =>
          p.extension.selectedPositionId != c.extension.selectedPositionId,
      builder: (context, state) {
        // Debug every tile to see what's happening
        final offerPosId = offer.positionId.toString();
        final selectedPosId = state.extension.selectedPositionId;
        print('Tile: ${offer.supplier} ($offerPosId) vs Selected ($selectedPosId) = ${offerPosId == selectedPosId}');

        return ListTile(
          title: Row(
            children: [
              Flexible(
                child: Text(
                  offer.supplier,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (offer.isCoreItem)
                const Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: V3ThemeWrapper(child: CoreProductTag()),
                ),
            ],
          ),
          trailing: Radio<String>(
            value: offer.positionId.toString(),
            groupValue: state.extension.selectedPositionId,
            onChanged: (_) => cubit.selectSupplier(
              offer.positionId.toString(),
            ),
          ),
          onTap: () => cubit.selectSupplier(
            offer.positionId.toString(),
          ),
        );
      },
    );
  }
}
