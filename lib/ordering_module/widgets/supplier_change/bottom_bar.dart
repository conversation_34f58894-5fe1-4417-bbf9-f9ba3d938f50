import 'package:app/i18n/i18n.dart';
import 'package:app/ordering_module/cubits/supplier_change/cubit.dart';
import 'package:app/shared/helpers/helpers.dart';
import 'package:app/shared/widgets/bottom_action_bar.dart';
import 'package:app/shared/widgets/responsive_elevated_button.dart';
import 'package:app/shared/widgets/wrapper/disabled_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SupplierChangeBottomBar extends StatelessWidget {
  const SupplierChangeBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return Padding(
      padding: MediaQuery.of(context).viewInsets,
      child: BottomActionBar(
        child: BlocBuilder<SupplierChangeCubit, SupplierChangeState>(
          buildWhen: (p, c) {
            return p.extension.selectedPositionId !=
                    c.extension.selectedPositionId ||
                p.isOnline != c.isOnline;
          },
          builder: (context, state) {
            return DisabledBuilder(
              conditions: [
                DisableCondition(
                  condition: state.extension.defaultPositionId ==
                      state.extension.selectedPositionId,
                ),
                DisableCondition(
                  condition: !state.isOnline,
                  message: tr(
                    Labels.order_lists.supplier_change
                        .action_available_only_online,
                  ),
                ),
              ],
              builder: (context, isDisabled) {
                return ResponsiveElevatedButton(
                  text: tr(Labels.order_lists.supplier_change.button_label),
                  onPressed: isDisabled
                      ? null
                      : () => Navigator.of(context).pop(
                            state.entities
                                .where(
                                  (o) =>
                                      o.positionId.toString() ==
                                      state.extension.selectedPositionId,
                                )
                                .first,
                          ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
