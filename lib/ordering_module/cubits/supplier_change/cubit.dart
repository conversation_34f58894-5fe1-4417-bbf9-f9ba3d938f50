import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/shared/cubits/pageless_entity_list/cubit.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit.freezed.dart';

@freezed
class SupplierChangeStateExtension with _$SupplierChangeStateExtension {
  const SupplierChangeStateExtension._();

  const factory SupplierChangeStateExtension({
    required String metaId,
    required String defaultPositionId,
    required String selectedPositionId,
    required String language,
  }) = _SupplierChangeStateExtension;
}

typedef SupplierChangeState
    = PagelessEntityListState<ProductOfferModel, SupplierChangeStateExtension>;

class SupplierChangeCubit extends PagelessEntityListCubit<ProductOfferModel,
    SupplierChangeStateExtension> {
  final CatalogRepository _repository;

  SupplierChangeCubit(
    this._repository, {
    required String metaId,
    required String defaultPositionId,
    required bool isOnline,
    required String language,
  }) : super(
          initialStateExtension: SupplierChangeStateExtension(
            metaId: metaId,
            defaultPositionId: defaultPositionId,
            selectedPositionId: defaultPositionId,
            language: language,
          ),
          isOnline: isOnline,
        ) {
    print('SupplierChangeCubit created with defaultPositionId: $defaultPositionId, selectedPositionId: $defaultPositionId');
  }

  @override
  Future<Result<List<ProductOfferModel>, AppError>> loadEntities() async {
    final result = await _repository.getProductOffers(
      metaId: state.extension.metaId,
      language: state.extension.language,
    );

    // Simple fix: After loading offers, ensure selectedPositionId matches an available offer
    result.match(
      ok: (offers) {
        final currentSelected = state.extension.selectedPositionId;
        final hasMatch = offers.any((offer) => offer.positionId.toString() == currentSelected);

        if (!hasMatch && offers.isNotEmpty) {
          // If current selection doesn't match any offer, select the first one
          final firstOfferId = offers.first.positionId.toString();
          print('No match for $currentSelected, selecting first available: $firstOfferId');

          // Update the selected position ID to match an available offer
          emit(state.copyWith(
            extension: state.extension.copyWith(
              selectedPositionId: firstOfferId,
            ),
          ));
        }
      },
      err: (_) {}, // Do nothing on error
    );

    return result;
  }

  void selectSupplier(String positionId) {
    if (positionId == state.extension.selectedPositionId) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          selectedPositionId: positionId,
        ),
      ),
    );
  }

  void updateSelectedPositionId(String newPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(selectedPositionId: newPositionId),
      ),
    );
  }
}
