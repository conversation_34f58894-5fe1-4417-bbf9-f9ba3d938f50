import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/shared/cubits/pageless_entity_list/cubit.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit.freezed.dart';

@freezed
class SupplierChangeStateExtension with _$SupplierChangeStateExtension {
  const SupplierChangeStateExtension._();

  const factory SupplierChangeStateExtension({
    required String metaId,
    required String defaultPositionId,
    required String selectedPositionId,
    required String language,
  }) = _SupplierChangeStateExtension;
}

typedef SupplierChangeState
    = PagelessEntityListState<ProductOfferModel, SupplierChangeStateExtension>;

class SupplierChangeCubit extends PagelessEntityListCubit<ProductOfferModel,
    SupplierChangeStateExtension> {
  final CatalogRepository _repository;

  SupplierChangeCubit(
    this._repository, {
    required String metaId,
    required String defaultPositionId,
    required bool isOnline,
    required String language,
  }) : super(
          initialStateExtension: SupplierChangeStateExtension(
            metaId: metaId,
            defaultPositionId: defaultPositionId,
            selectedPositionId: defaultPositionId,
            language: language,
          ),
          isOnline: isOnline,
        );

  @override
  Future<Result<List<ProductOfferModel>, AppError>> loadEntities() async {
    final result = await _repository.getProductOffers(
      metaId: state.extension.metaId,
      language: state.extension.language,
    );

    // After loading entities, ensure the selected position ID is valid
    result.match(
      ok: (offers) {
        final currentSelectedId = state.extension.selectedPositionId;
        final hasMatchingOffer = offers.any(
          (offer) => offer.positionId.toString() == currentSelectedId,
        );

        // If the current selected position ID doesn't match any offer,
        // reset to the default position ID
        if (!hasMatchingOffer) {
          emit(
            state.copyWith(
              extension: state.extension.copyWith(
                selectedPositionId: state.extension.defaultPositionId,
              ),
            ),
          );
        }
      },
      err: (_) {
        // Error case - no action needed as the parent will handle it
      },
    );

    return result;
  }

  void selectSupplier(String positionId) {
    if (positionId == state.extension.selectedPositionId) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          selectedPositionId: positionId,
        ),
      ),
    );
  }

  void updateSelectedPositionId(String newPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(selectedPositionId: newPositionId),
      ),
    );
  }

  void updateDefaultPositionId(String newDefaultPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          defaultPositionId: newDefaultPositionId,
          selectedPositionId: newDefaultPositionId,
        ),
      ),
    );
  }

  Future<void> refreshOffers() async {
    await load();
  }
}
