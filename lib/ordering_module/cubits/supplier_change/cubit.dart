import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/shared/cubits/pageless_entity_list/cubit.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit.freezed.dart';

@freezed
class SupplierChangeStateExtension with _$SupplierChangeStateExtension {
  const SupplierChangeStateExtension._();

  const factory SupplierChangeStateExtension({
    required String metaId,
    required String defaultPositionId,
    required String selectedPositionId,
    required String language,
  }) = _SupplierChangeStateExtension;
}

typedef SupplierChangeState
    = PagelessEntityListState<ProductOfferModel, SupplierChangeStateExtension>;

class SupplierChangeCubit extends PagelessEntityListCubit<ProductOfferModel,
    SupplierChangeStateExtension> {
  final CatalogRepository _repository;

  SupplierChangeCubit(
    this._repository, {
    required String metaId,
    required String defaultPositionId,
    required bool isOnline,
    required String language,
  }) : super(
          initialStateExtension: SupplierChangeStateExtension(
            metaId: metaId,
            defaultPositionId: defaultPositionId,
            selectedPositionId: defaultPositionId,
            language: language,
          ),
          isOnline: isOnline,
        ) {
    print('SupplierChangeCubit created with defaultPositionId: $defaultPositionId, selectedPositionId: $defaultPositionId');
  }

  @override
  Future<Result<List<ProductOfferModel>, AppError>> loadEntities() {
    return _repository.getProductOffers(
      metaId: state.extension.metaId,
      language: state.extension.language,
    );
  }

  @override
  Future<void> load({bool background = false}) async {
    await super.load(background: background);

    // After loading is complete, fix the selection if needed
    if (state.listStatus == PagelessEntityListStatus.load_success) {
      _fixSelection();
    }
  }

  Future<void> _fixSelection() async {
    final currentSelected = state.extension.selectedPositionId;
    final offers = state.entities;

    if (offers.isEmpty) return;

    final hasMatch = offers.any((offer) => offer.positionId.toString() == currentSelected);

    if (!hasMatch) {
      final firstOfferId = offers.first.positionId.toString();
      print('Fixing selection: $currentSelected -> $firstOfferId');

      // Small delay to ensure UI is ready
      await Future.delayed(const Duration(milliseconds: 100));

      emit(
        state.copyWith(
          extension: state.extension.copyWith(
            selectedPositionId: firstOfferId,
          ),
        ),
      );
    }
  }

  void selectSupplier(String positionId) {
    if (positionId == state.extension.selectedPositionId) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          selectedPositionId: positionId,
        ),
      ),
    );
  }

  void updateSelectedPositionId(String newPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(selectedPositionId: newPositionId),
      ),
    );
  }
}
