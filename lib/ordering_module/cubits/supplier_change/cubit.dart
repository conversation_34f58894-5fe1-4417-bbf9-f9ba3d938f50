import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/shared/cubits/pageless_entity_list/cubit.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit.freezed.dart';

@freezed
class SupplierChangeStateExtension with _$SupplierChangeStateExtension {
  const SupplierChangeStateExtension._();

  const factory SupplierChangeStateExtension({
    required String metaId,
    required String defaultPositionId,
    required String selectedPositionId,
    required String language,
  }) = _SupplierChangeStateExtension;
}

typedef SupplierChangeState
    = PagelessEntityListState<ProductOfferModel, SupplierChangeStateExtension>;

class SupplierChangeCubit extends PagelessEntityListCubit<ProductOfferModel,
    SupplierChangeStateExtension> {
  final CatalogRepository _repository;

  SupplierChangeCubit(
    this._repository, {
    required String metaId,
    required String defaultPositionId,
    required bool isOnline,
    required String language,
  }) : super(
          initialStateExtension: SupplierChangeStateExtension(
            metaId: metaId,
            defaultPositionId: defaultPositionId,
            selectedPositionId: defaultPositionId,
            language: language,
          ),
          isOnline: isOnline,
        );

  @override
  Future<Result<List<ProductOfferModel>, AppError>> loadEntities() async {
    final result = await _repository.getProductOffers(
      metaId: state.extension.metaId,
      language: state.extension.language,
    );

    return result;
  }

  @override
  Future<void> load({bool background = false}) async {
    // Call the parent load method first
    await super.load(background: background);

    // After loading is complete, validate and fix the selected position ID
    if (state.listStatus == PagelessEntityListStatus.load_success) {
      _validateAndFixSelectedPositionId();
    }
  }

  @override
  Future<void> reload({bool background = false}) async {
    // Force a fresh load of offers
    await super.reload(background: background);

    // After reloading is complete, validate and fix the selected position ID
    if (state.listStatus == PagelessEntityListStatus.load_success) {
      _validateAndFixSelectedPositionId();
    }
  }

  void _validateAndFixSelectedPositionId() {
    final currentSelectedId = state.extension.selectedPositionId;
    final defaultPositionId = state.extension.defaultPositionId;
    final offers = state.entities;

    // Debug: Log the validation attempt
    print('=== SupplierChange Validation ===');
    print('Current Selected ID: $currentSelectedId');
    print('Default Position ID: $defaultPositionId');
    print('Available Offers: ${offers.map((o) => '${o.positionId}:${o.supplier}').join(', ')}');

    if (offers.isEmpty) {
      print('No offers available, skipping validation');
      return;
    }

    final hasMatchingOffer = offers.any(
      (offer) => offer.positionId.toString() == currentSelectedId,
    );

    print('Has matching offer for current selected: $hasMatchingOffer');

    // If the current selected position ID doesn't match any offer,
    // check if the default position ID matches any offer
    if (!hasMatchingOffer) {
      final hasMatchingDefault = offers.any(
        (offer) => offer.positionId.toString() == defaultPositionId,
      );

      print('Has matching offer for default: $hasMatchingDefault');

      final newSelectedId = hasMatchingDefault
          ? defaultPositionId
          : offers.first.positionId.toString();

      print('New selected ID will be: $newSelectedId');

      if (newSelectedId != currentSelectedId) {
        print('Updating selected ID from $currentSelectedId to $newSelectedId');
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              selectedPositionId: newSelectedId,
            ),
          ),
        );
      }
    } else {
      print('Current selection is valid, no change needed');
    }
    print('=== End Validation ===');
  }

  void selectSupplier(String positionId) {
    if (positionId == state.extension.selectedPositionId) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          selectedPositionId: positionId,
        ),
      ),
    );
  }

  void updateSelectedPositionId(String newPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(selectedPositionId: newPositionId),
      ),
    );
  }

  void updateDefaultPositionId(String newDefaultPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          defaultPositionId: newDefaultPositionId,
          selectedPositionId: newDefaultPositionId,
        ),
      ),
    );
  }

  Future<void> refreshOffers() async {
    await reload();
  }

  void updateProductState(int newPositionId) {
    final newDefaultPositionId = newPositionId.toString();
    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          defaultPositionId: newDefaultPositionId,
          selectedPositionId: newDefaultPositionId,
        ),
      ),
    );
  }
}
