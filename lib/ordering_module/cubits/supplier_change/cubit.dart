import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/shared/cubits/pageless_entity_list/cubit.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'cubit.freezed.dart';

@freezed
class SupplierChangeStateExtension with _$SupplierChangeStateExtension {
  const SupplierChangeStateExtension._();

  const factory SupplierChangeStateExtension({
    required String metaId,
    required String defaultPositionId,
    required String selectedPositionId,
    required String language,
  }) = _SupplierChangeStateExtension;
}

typedef SupplierChangeState
    = PagelessEntityListState<ProductOfferModel, SupplierChangeStateExtension>;

class SupplierChangeCubit extends PagelessEntityListCubit<ProductOfferModel,
    SupplierChangeStateExtension> {
  final CatalogRepository _repository;

  SupplierChangeCubit(
    this._repository, {
    required String metaId,
    required String defaultPositionId,
    required bool isOnline,
    required String language,
  }) : super(
          initialStateExtension: SupplierChangeStateExtension(
            metaId: metaId,
            defaultPositionId: defaultPositionId,
            selectedPositionId: defaultPositionId,
            language: language,
          ),
          isOnline: isOnline,
        );

  @override
  Future<Result<List<ProductOfferModel>, AppError>> loadEntities() async {
    final result = await _repository.getProductOffers(
      metaId: state.extension.metaId,
      language: state.extension.language,
    );

    return result;
  }

  @override
  Future<void> load({bool background = false}) async {
    // Call the parent load method first
    await super.load(background: background);

    // After loading is complete, validate and fix the selected position ID
    if (state.listStatus == PagelessEntityListStatus.load_success) {
      _validateAndFixSelectedPositionId();
    }
  }

  void _validateAndFixSelectedPositionId() {
    final currentSelectedId = state.extension.selectedPositionId;
    final defaultPositionId = state.extension.defaultPositionId;
    final offers = state.entities;

    // Debug information
    print('SupplierChangeCubit Debug:');
    print('  Current selected ID: $currentSelectedId');
    print('  Default position ID: $defaultPositionId');
    print('  Available offers: ${offers.map((o) => '${o.positionId}:${o.supplier}').join(', ')}');

    final hasMatchingOffer = offers.any(
      (offer) => offer.positionId.toString() == currentSelectedId,
    );

    print('  Has matching offer for selected ID: $hasMatchingOffer');

    // If the current selected position ID doesn't match any offer,
    // check if the default position ID matches any offer
    if (!hasMatchingOffer) {
      final hasMatchingDefault = offers.any(
        (offer) => offer.positionId.toString() == defaultPositionId,
      );

      print('  Has matching offer for default ID: $hasMatchingDefault');

      String? newSelectedId;

      if (hasMatchingDefault) {
        // Reset to default position ID
        newSelectedId = defaultPositionId;
        print('  Resetting to default position ID: $newSelectedId');
      } else if (offers.isNotEmpty) {
        // Neither selected nor default matches, select the first available offer
        newSelectedId = offers.first.positionId.toString();
        print('  Selecting first available offer: $newSelectedId');
      }

      if (newSelectedId != null) {
        emit(
          state.copyWith(
            extension: state.extension.copyWith(
              selectedPositionId: newSelectedId,
            ),
          ),
        );
      }
    }
  }

  void selectSupplier(String positionId) {
    if (positionId == state.extension.selectedPositionId) {
      return;
    }

    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          selectedPositionId: positionId,
        ),
      ),
    );
  }

  void updateSelectedPositionId(String newPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(selectedPositionId: newPositionId),
      ),
    );
  }

  void updateDefaultPositionId(String newDefaultPositionId) {
    emit(
      state.copyWith(
        extension: state.extension.copyWith(
          defaultPositionId: newDefaultPositionId,
          selectedPositionId: newDefaultPositionId,
        ),
      ),
    );
  }

  Future<void> refreshOffers() async {
    await load();
  }
}
