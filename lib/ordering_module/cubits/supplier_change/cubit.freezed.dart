// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SupplierChangeStateExtension {
  String get metaId => throw _privateConstructorUsedError;
  String get defaultPositionId => throw _privateConstructorUsedError;
  String get selectedPositionId => throw _privateConstructorUsedError;
  String get language => throw _privateConstructorUsedError;

  /// Create a copy of SupplierChangeStateExtension
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SupplierChangeStateExtensionCopyWith<SupplierChangeStateExtension>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SupplierChangeStateExtensionCopyWith<$Res> {
  factory $SupplierChangeStateExtensionCopyWith(
          SupplierChangeStateExtension value,
          $Res Function(SupplierChangeStateExtension) then) =
      _$SupplierChangeStateExtensionCopyWithImpl<$Res,
          SupplierChangeStateExtension>;
  @useResult
  $Res call(
      {String metaId,
      String defaultPositionId,
      String selectedPositionId,
      String language});
}

/// @nodoc
class _$SupplierChangeStateExtensionCopyWithImpl<$Res,
        $Val extends SupplierChangeStateExtension>
    implements $SupplierChangeStateExtensionCopyWith<$Res> {
  _$SupplierChangeStateExtensionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SupplierChangeStateExtension
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? metaId = null,
    Object? defaultPositionId = null,
    Object? selectedPositionId = null,
    Object? language = null,
  }) {
    return _then(_value.copyWith(
      metaId: null == metaId
          ? _value.metaId
          : metaId // ignore: cast_nullable_to_non_nullable
              as String,
      defaultPositionId: null == defaultPositionId
          ? _value.defaultPositionId
          : defaultPositionId // ignore: cast_nullable_to_non_nullable
              as String,
      selectedPositionId: null == selectedPositionId
          ? _value.selectedPositionId
          : selectedPositionId // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SupplierChangeStateExtensionImplCopyWith<$Res>
    implements $SupplierChangeStateExtensionCopyWith<$Res> {
  factory _$$SupplierChangeStateExtensionImplCopyWith(
          _$SupplierChangeStateExtensionImpl value,
          $Res Function(_$SupplierChangeStateExtensionImpl) then) =
      __$$SupplierChangeStateExtensionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String metaId,
      String defaultPositionId,
      String selectedPositionId,
      String language});
}

/// @nodoc
class __$$SupplierChangeStateExtensionImplCopyWithImpl<$Res>
    extends _$SupplierChangeStateExtensionCopyWithImpl<$Res,
        _$SupplierChangeStateExtensionImpl>
    implements _$$SupplierChangeStateExtensionImplCopyWith<$Res> {
  __$$SupplierChangeStateExtensionImplCopyWithImpl(
      _$SupplierChangeStateExtensionImpl _value,
      $Res Function(_$SupplierChangeStateExtensionImpl) _then)
      : super(_value, _then);

  /// Create a copy of SupplierChangeStateExtension
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? metaId = null,
    Object? defaultPositionId = null,
    Object? selectedPositionId = null,
    Object? language = null,
  }) {
    return _then(_$SupplierChangeStateExtensionImpl(
      metaId: null == metaId
          ? _value.metaId
          : metaId // ignore: cast_nullable_to_non_nullable
              as String,
      defaultPositionId: null == defaultPositionId
          ? _value.defaultPositionId
          : defaultPositionId // ignore: cast_nullable_to_non_nullable
              as String,
      selectedPositionId: null == selectedPositionId
          ? _value.selectedPositionId
          : selectedPositionId // ignore: cast_nullable_to_non_nullable
              as String,
      language: null == language
          ? _value.language
          : language // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SupplierChangeStateExtensionImpl extends _SupplierChangeStateExtension {
  const _$SupplierChangeStateExtensionImpl(
      {required this.metaId,
      required this.defaultPositionId,
      required this.selectedPositionId,
      required this.language})
      : super._();

  @override
  final String metaId;
  @override
  final String defaultPositionId;
  @override
  final String selectedPositionId;
  @override
  final String language;

  @override
  String toString() {
    return 'SupplierChangeStateExtension(metaId: $metaId, defaultPositionId: $defaultPositionId, selectedPositionId: $selectedPositionId, language: $language)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SupplierChangeStateExtensionImpl &&
            (identical(other.metaId, metaId) || other.metaId == metaId) &&
            (identical(other.defaultPositionId, defaultPositionId) ||
                other.defaultPositionId == defaultPositionId) &&
            (identical(other.selectedPositionId, selectedPositionId) ||
                other.selectedPositionId == selectedPositionId) &&
            (identical(other.language, language) ||
                other.language == language));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, metaId, defaultPositionId, selectedPositionId, language);

  /// Create a copy of SupplierChangeStateExtension
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SupplierChangeStateExtensionImplCopyWith<
          _$SupplierChangeStateExtensionImpl>
      get copyWith => __$$SupplierChangeStateExtensionImplCopyWithImpl<
          _$SupplierChangeStateExtensionImpl>(this, _$identity);
}

abstract class _SupplierChangeStateExtension
    extends SupplierChangeStateExtension {
  const factory _SupplierChangeStateExtension(
      {required final String metaId,
      required final String defaultPositionId,
      required final String selectedPositionId,
      required final String language}) = _$SupplierChangeStateExtensionImpl;
  const _SupplierChangeStateExtension._() : super._();

  @override
  String get metaId;
  @override
  String get defaultPositionId;
  @override
  String get selectedPositionId;
  @override
  String get language;

  /// Create a copy of SupplierChangeStateExtension
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SupplierChangeStateExtensionImplCopyWith<
          _$SupplierChangeStateExtensionImpl>
      get copyWith => throw _privateConstructorUsedError;
}
