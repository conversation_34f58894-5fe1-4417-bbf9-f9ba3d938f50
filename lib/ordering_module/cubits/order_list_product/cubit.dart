import 'package:app/ordering_module/cubits/order_list_product/state.dart';
import 'package:app/ordering_module/models/order_list_product.dart';
import 'package:app/shared/models/cart_product_totals.dart';
import 'package:app/shared/repositories/order_lists.dart';
import 'package:app/shared/types/app_error.dart';
import 'package:app/shared/types/result.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class OrderListProductCubit extends Cubit<OrderListProductState> {
  final OrderListsRepository _repository;

  OrderListProductCubit(
    this._repository, {
    required bool isReadOnly,
    required int orderListId,
    required OrderListProductModel product,
    required bool isOnline,
  }) : super(
          OrderListProductState(
            status: OrderListProductStatus.load_success,
            isReadOnly: isReadOnly,
            orderListId: orderListId,
            product: product,
            isOnline: isOnline,
          ),
        );

  void updateNetworkState(bool isOnline) {
    if ((!isOnline && !state.isOnline) || (isOnline && state.isOnline)) {
      return;
    }

    emit(
      state.copyWith(
        isOnline: isOnline,
      ),
    );
  }

  Future<void> updateItemSupplier({
    required int replacementPositionId,
    required String replacementItemType,
  }) async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    final result = await _repository.replaceProduct(
      listId: state.orderListId,
      positionId: state.product.positionId,
      itemType: state.product.type,
      replacementPositionId: replacementPositionId,
      replacementItemType: replacementItemType,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.supplier_change_success,
            product: state.product.copyWith(
              positionId: replacementPositionId,
            ),
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }

  Future<void> editComment(String comment) async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    Result<bool, AppError> result = await _repository.updateProductComment(
      listId: state.orderListId,
      listItemId: state.product.id,
      comment: comment,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.comment_edit_success,
            product: state.product.copyWith(comment: comment),
          ),
        );

        emit(
          state.copyWith(
            status: OrderListProductStatus.load_success,
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }

  Future<void> deleteComment() async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    Result<bool, AppError> result = await _repository.updateProductComment(
      listId: state.orderListId,
      listItemId: state.product.id,
      comment: null,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.comment_delete_success,
            product: state.product.copyWith(comment: null),
          ),
        );

        emit(
          state.copyWith(
            status: OrderListProductStatus.load_success,
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }

  Future<void> addComment(String comment) async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    Result<bool, AppError> result = await _repository.updateProductComment(
      listId: state.orderListId,
      listItemId: state.product.id,
      comment: comment,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.comment_add_success,
            product: state.product.copyWith(comment: comment),
          ),
        );

        emit(
          state.copyWith(
            status: OrderListProductStatus.load_success,
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }

  Future<void> updateCartTotals(
    CartProductTotalsModel cartProductTotals,
  ) async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.load_success,
        product: state.product.copyWith(
          totalPriceInCart: cartProductTotals.totalPriceInCart,
          priceLevels: cartProductTotals.priceLevels,
          quantityInCart: cartProductTotals.quantityInCart,
        ),
      ),
    );
  }

  Future<void> deleteProduct() async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    Result<bool, AppError> result = await _repository.deleteProduct(
      listId: state.orderListId,
      product: state.product,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.delete_success,
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }

  Future<void> copyItemToList(int listId) async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    Result<bool, AppError> result = await _repository.copyProduct(
      sourceListId: state.orderListId,
      targetListId: listId,
      positionId: state.product.positionId,
      itemType: state.product.type,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.copy_success,
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }

  Future<void> moveItemToList(int listId) async {
    emit(
      state.copyWith(
        status: OrderListProductStatus.operation_in_progress,
      ),
    );

    Result<bool, AppError> result = await _repository.moveProduct(
      sourceListId: state.orderListId,
      targetListId: listId,
      positionId: state.product.positionId,
      itemType: state.product.type,
    );

    result.match(
      ok: (_) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.move_success,
          ),
        );
      },
      err: (error) {
        emit(
          state.copyWith(
            status: OrderListProductStatus.operation_error,
            error: error,
          ),
        );
      },
    );
  }
}
