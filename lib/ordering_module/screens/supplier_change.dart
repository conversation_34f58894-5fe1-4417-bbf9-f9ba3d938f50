import 'package:app/catalog_module/models/product_offer.dart';
import 'package:app/i18n/i18n.dart';
import 'package:app/ordering_module/cubits/supplier_change/cubit.dart';
import 'package:app/ordering_module/models/order_list_product.dart';
import 'package:app/ordering_module/widgets/supplier_change/bottom_bar.dart';
import 'package:app/ordering_module/widgets/supplier_change/list.dart';
import 'package:app/shared/cubits/api_connectivity/cubit.dart';
import 'package:app/shared/helpers/i18n.dart';
import 'package:app/shared/repositories/catalog.dart';
import 'package:app/shared/screens/app_menu.dart';
import 'package:app/shared/widgets/dynamic_app_bar.dart';
import 'package:app/shared/widgets/wide_screen_wrapper.dart';
import 'package:app/shared/widgets/wrapper/api_connectivity_listener.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SupplierChangeScreen extends StatelessWidget {
  final OrderListProductModel product;

  const SupplierChangeScreen({
    super.key,
    required this.product,
  });

  static MaterialPageRoute<ProductOfferModel> route({
    required OrderListProductModel product,
  }) {
    return MaterialPageRoute<ProductOfferModel>(
      settings: RouteSettings(
        name: '/order_list_product_supplier_change',
        arguments: {'id': product.id},
      ),
      builder: (_) => SupplierChangeScreen(product: product),
    );
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<SupplierChangeCubit>(
          create: (context) {
            final cubit = SupplierChangeCubit(
              context.read<CatalogRepository>(),
              metaId: product.metaId,
              defaultPositionId: product.positionId.toString(),
              isOnline: context.read<ApiConnectivityCubit>().state,
              language: context.read<LocalizationState>().locale.toString(),
            );

            // Force a fresh load of offers to ensure we have the latest data
            cubit.reload();

            return cubit;
          },
        ),
      ],
      child: MultiBlocListener(
        listeners: [
          ApiConnectivityListener(
            onConnectionStateChanged: (context, state) {
              context.read<SupplierChangeCubit>().updateNetworkState(state);
            },
          ),
        ],
        child: const SupplierChangeView(),
      ),
    );
  }
}

class SupplierChangeView extends StatelessWidget {
  const SupplierChangeView({super.key});

  @override
  Widget build(BuildContext context) {
    final tr = getTranslator(context);

    return Scaffold(
      drawer: const AppMenu(),
      bottomNavigationBar: const SupplierChangeBottomBar(),
      appBar: DynamicAppBar(
        title: Text(
          tr(Labels.order_lists.supplier_change.title),
        ),
      ),
      body: const WideScreenWrapper(
        child: SupplierChangeList(),
      ),
    );
  }
}
